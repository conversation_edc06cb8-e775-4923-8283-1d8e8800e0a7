const redirectSignIn =
  typeof window !== "undefined"
    ? `${window.location.origin}/login`
    : "http://localhost:3000/login";
const redirectSignOut =
  typeof window !== "undefined"
    ? `${window.location.origin}/login`
    : "http://localhost:3000/application";

export const hzuAwsConfig = {
  aws_project_region: "eu-west-1",
  aws_cognito_identity_pool_id:
    "eu-west-1:e4981f38-fd20-4ae4-b3dc-c4e99eedc644",
  aws_cognito_region: "eu-west-1",
  aws_user_pools_id: "eu-west-1_W2pHL0wmd",
  aws_user_pools_web_client_id: "5t0r9uvee31oqnba1j4rr0uuo2",
  oauth: {
    domain: "apphero-dev.auth.eu-west-1.amazoncognito.com",
    redirectSignIn,
    redirectSignOut,
    responseType: "code",
    scope: [
      "email",
      "openid",
      "phone",
      "profile",
      "aws.cognito.signin.user.admin",
    ],
  },
};

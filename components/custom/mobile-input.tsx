"use client";
import IntlTelInput from "react-intl-tel-input";
import "react-intl-tel-input/dist/main.css";
import "./mobile-input.css";
import { useState, useLayoutEffect } from "react";

interface MobileInputProps {
  handleChange?: Function;
  selectedValue: any;
  register?: any;
  onBlur?: any;
  handleCountry?: any;
  defaultCountry?: string;
  errorMessage?: string;
  placeholder?: string;
  handleFlagChange?: any;
}

export function MobileInput({
  selectedValue,
  handleChange = () => {},
  register,
  onBlur,
  defaultCountry,
  errorMessage,
  placeholder,
  handleFlagChange,
}: MobileInputProps) {
  const [selectedFlag, setSelectedFlag] = useState<any>();
  return (
    <div {...register}>
      <IntlTelInput
        value={selectedValue}
        containerClassName={`intl-tel-input w-full border ${
          errorMessage ? "border-error" : "border-border"
        }  rounded`}
        inputClassName="form-control w-full border-border rounded h-[38px]"
        telInputProps={{
          required: true,
        }}
        separateDialCode
        onPhoneNumberChange={(
          status,
          phoneNumber,
          country,
          formattedNumber
        ) => {
          handleChange(status, phoneNumber, country, formattedNumber);
        }}
        defaultCountry={defaultCountry || selectedFlag || "us"}
        onSelectFlag={(
          currentNumber,
          selectedCountryData,
          fullNumber,
          isValid
        ) => {
          setSelectedFlag(selectedCountryData?.iso2);
          handleFlagChange();
        }}
        onPhoneNumberBlur={onBlur}
        placeholder={placeholder}
      />
    </div>
  );
}

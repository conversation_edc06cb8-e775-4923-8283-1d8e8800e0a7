import { Checkbox } from "../ui/checkbox";
import { FieldTitle } from "./FieldTitle";

interface RadioButtonProps {
  register?: any;
  label: string;
  fieldItem?: any;
  isMandatory?: boolean;
  selectedValue?: boolean;
  handleChange?: any;
  disabled?: boolean;
  errorMessage?: string;
}

export function CheckBox(props: RadioButtonProps) {
  const {
    register,
    label,
    fieldItem,
    isMandatory,
    selectedValue,
    handleChange,
    disabled,
    errorMessage,
  } = props;

  return (
    <div className={`w-full mb-5 space-x-2 flex items-start`} {...register}>
      <Checkbox
        id={`${"checkbox" + label}`}
        defaultChecked={selectedValue}
        checked={selectedValue}
        onCheckedChange={(type) => handleChange(type)}
        disabled={disabled}
        name={fieldItem?.name}
        className={`rounded-[2px] bg-background border ${
          errorMessage ? "border-error" : "border-border"
        } text-background data-[state=checked]:text-background data-[state=checked]:bg-primary p-0 mt-1.5`}
      />

      <FieldTitle
        label={label}
        htmlFor={`${"checkbox" + label}`}
        isMandatory={!fieldItem?.displayName && isMandatory}
      />
    </div>
  );
}

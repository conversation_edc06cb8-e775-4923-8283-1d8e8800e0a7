"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { sortOrder } from "@/helpers/Sorting";
import { getOapDetail, getOapForm, getOapFormSections } from "@/api/api";
import loader2 from "../../public/loader2.svg";
import loader from "../../public/loader.svg";
import { useAtom } from "jotai";
import {
  consumerAPIKey,
  dateReplacement,
  preferredDateFormat,
  preferredLanguage,
  staticContentsAtom,
} from "@/lib/atom";
import { useFormContext } from "react-hook-form";
import DynamicFields from "../custom/DynamicFields";
import { FormLayout } from "../custom/formLayout";
import ReactMarkdown from "react-markdown";
import { resetPassword } from "aws-amplify/auth";
import { getFieldNamesByFormQuery } from "@/lib/utils";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

export default function ForgotPassword() {
  const [pageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });
  const [staticContent] = useAtom<any>(staticContentsAtom);

  const router = useRouter();
  const forceResetPassword = localStorage.getItem("forceResetPassword");

  const [saving, setSaving] = useState<boolean>(false);
  const [apiKey, setApiKey] = useAtom(consumerAPIKey);
  const [, setPreferredDateFormat] = useAtom(preferredDateFormat);
  const [, setDateReplacement] = useAtom(dateReplacement);
  const [showVerficationMessage, setShowVerficationMessage] = useState(false);
  const [preferLang] = useAtom(preferredLanguage);

  const [fontSize, setFontSize] = useAtom(fontSizeAtom);

  const {
    register,
    setValue,
    watch,
    formState: { errors },
    clearErrors,
    trigger,
    setError,
    getValues,
  } = useFormContext();

  const {
    data: pageQuery,
    isFetching: pageQueryFetching,
    refetch: refetchPageQuery,
  } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
          ...(preferLang === "de" && { language: preferLang }),
        },
        apiKey
      );
      setPreferredDateFormat(res?.preferedDateFormat);
      setDateReplacement(res?.replaceWith);
      setApiKey(res?.eipConsumerKey);
      if (res?.fontSize) {
        setFontSize(res.fontSize);
      }
      return res;
    },
    enabled: true,
  });

  const {
    data: sectionQuery,
    isFetching: sectionQueryFetching,
    refetch: refetchSectionQuery,
  } = useQuery({
    queryKey: [`${pageQuery?.PK}-${pageQuery?.SK}-${pageQuery?.landingForm}`],
    queryFn: async () => {
      let res = await getOapForm(
        {
          oap: pageQuery?.PK,
          form: pageQuery?.forgotPasswordForm,
          mode: pageQuery?.SK,
          ...(preferLang === "de" && { language: preferLang }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!pageQuery?.PK,
  });

  const {
    data: formQuery,
    isFetching: formQueryFetching,
    refetch: refetchFormQuery,
  } = useQuery({
    queryKey: [
      `${pageQuery?.PK}-${pageQuery?.SK}-${sectionQuery?.SK}-${sectionQuery?.section?.[0]?.section}`,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[0]?.section && sectionQuery?.SK)) return;
      let res = await getOapFormSections(
        {
          oap: pageQuery?.PK,
          mode: pageQuery?.SK,
          formName: sectionQuery?.SK,
          sectionName: sectionQuery?.section?.[0]?.section,
          ...(preferLang === "de" && { language: preferLang }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!sectionQuery?.SK,
  });

  useEffect(() => {
    refetchPageQuery();
    if (pageDetails?.screen && pageDetails.mode && pageQuery?.SK) {
      refetchSectionQuery();
      refetchFormQuery();
    }
  }, [preferLang]);

  useEffect(() => {
    if (forceResetPassword) {
      localStorage.setItem("forceResetPassword", "true");
    }
  }, [forceResetPassword]);

  useEffect(() => {
    return () => {
      localStorage.removeItem("forceResetPassword");
    };
  }, []);

  const handleForgotPassword = async () => {
    const rest = getValues();

    const fieldNames = getFieldNamesByFormQuery(formQuery);

    const isValid = await trigger(fieldNames);

    if (!isValid) return;

    rest.forgotPasswordEmail = rest?.forgotPasswordEmail?.toLowerCase();

    try {
      setSaving((prev) => !prev);

      const response = await resetPassword({
        username: rest?.forgotPasswordEmail,
      });

      if (response?.nextStep?.codeDeliveryDetails) {
        setShowVerficationMessage(true);
      }
      if (forceResetPassword) {
        localStorage.removeItem("forceResetPassword");
      }
    } catch (error: any) {
      setSaving((prev: any) => !prev);
      console.log("error", error.name);
      if (error?.message) {
        if (error?.name === "UserNotFoundException") {
          return setError("forgotPasswordEmail", {
            message:
              staticContent?.errors?.userValidation?.UserNotFoundException ||
              "User does not exist",
          });
        } else if (error?.name === "InvalidParameterException") {
          return setError("forgotPasswordEmail", {
            message:
              staticContent?.errors?.userValidation?.passwordResetFailed ||
              "Password reset failed.  Please confirm your email.",
          });
        }
        setError("forgotPasswordEmail", {
          message:
            staticContent?.errors?.userValidation?.[error?.name] ||
            error.message,
        });
      }
    }
  };
  if (showVerficationMessage) {
    return (
      <FormLayout pageQuery={pageQuery}>
        <div
          className=" w-full max-w-lg p-4 bg-background border border-gray-200 shadow sm:p-6  md:px-16 md:py-8"
          style={{ borderRadius: 8 }}
        >
          <div className="space-y-4">
            <h5 className="font-bold text-primary dark:text-white pb-6 text-center text-3xl">
              {formQuery?.verificationMessage?.displayName}
            </h5>
          </div>
          <div className="px-5">
            <ReactMarkdown className="markDown text-center">
              {formQuery?.verificationMessage?.message}
            </ReactMarkdown>
          </div>
        </div>
      </FormLayout>
    );
  }
  if (pageQueryFetching || sectionQueryFetching || formQueryFetching) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll bg-contain bg-no-repeat bg-center">
        <Image
          priority
          src={loader}
          height={32}
          width={32}
          alt="Follow us on Twitter"
        />
      </main>
    );
  }

  return (
    <FormLayout pageQuery={pageQuery}>
      <div
        className={`flex flex-col justify-center gap-y-10 lg:flex-row rounded-[24px] gap-x-10 
          ${
            sectionQuery.description
              ? "xl:w-[75%] mt-3 pt-4"
              : "xl:w-full justify-center"
          } `}
      >
        {sectionQuery?.description ? (
          <div className="flex-1 pr-8">
            <ReactMarkdown
              className="markDown"
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {sectionQuery?.description}
            </ReactMarkdown>
          </div>
        ) : null}

        <div
          className=" w-full max-w-[480px] h-fit pb-3 px-8 bg-background border border-gray-200 shadow mt-4"
          style={{ borderRadius: 8 }}
        >
          <div className="space-y-3 px-12 py-8">
            <h5
              className="font-bold text-text-primary dark:text-white text-center text-3xl pb-3"
              style={getBrandSpecificFontStyle(fontSize, "page-title")}
            >
              {sectionQuery?.section?.[0]?.displayName}
            </h5>
            {forceResetPassword ? (
              <div
                className="flex justify-center items-center w-full"
                style={{ minHeight: 30 }}
              >
                <span className="text-error text-center">
                  Please reset your password to continue.
                </span>
              </div>
            ) : null}
            {sortOrder(formQuery?.fieldData, "indexOrder")
              ?.filter((item: any) => item?.indexOrder <= 1)
              ?.map((item: any, index: any) => {
                return (
                  <div key={index}>
                    <DynamicFields
                      register={register}
                      selectedValue={
                        watch(item?.fieldName) ||
                        watch(`${item?.documentType}`) ||
                        ""
                      }
                      disabled={
                        item?.disabledWhen
                          ? watch(item?.disabledWhen?.fieldName)?.label ===
                            item?.disabledWhen?.value
                          : false
                      }
                      isVisibleWhen
                      fieldItem={item}
                      label={
                        item?.label || item?.displayName || item?.placeholder
                      }
                      handleValueChanged={(value: any, type?: string) => {
                        if (item?.childField && item?.setValue) {
                          if (value?.value == item?.value) {
                            setValue(item?.childField, item?.setValue);
                            clearErrors(item?.childField);
                          } else {
                            setValue(item?.childField, "");
                          }
                        }
                        clearErrors(item?.fieldName);
                        clearErrors(`${item?.documentType}`);
                        if (type === "pickList" && item?.fieldDisplayName) {
                          setValue(item?.fieldDisplayName, value);
                        }
                        if (item?.resetChild) {
                          setValue(item?.resetChild, "");
                          clearErrors(item?.resetChild);
                        }
                        setValue(item?.fieldName, value);
                      }}
                      errorMessage={
                        errors?.[item?.fieldName]?.message ||
                        errors?.[`${item?.documentType}`]?.message
                      }
                      name={item?.fieldName}
                      trigger={trigger}
                      watch={watch}
                      clearErrors={clearErrors}
                      setError={setError}
                      displayNoTitle={false}
                      setValue={setValue}
                    />
                  </div>
                );
              })}

            <div className="form-action w-full flex justify-center ">
              {formQuery?.fieldData
                ?.filter(
                  (item: any) => item.type == "button" && item?.indexOrder === 2
                )
                .map((ele: any, i: number) => {
                  return (
                    <button
                      key={i}
                      style={getBrandSpecificFontStyle(fontSize, "label")}
                      onClick={() => {
                        handleForgotPassword();
                      }}
                      className="text-background rounded  bg-primary w-full hover:bg-secondary font-bold  text-sm px-5 py-2.5"
                    >
                      {saving ? (
                        <div className=" w-full flex items-center justify-center">
                          <Image
                            priority
                            src={loader2}
                            height={20}
                            width={20}
                            alt="Follow us on Twitter"
                          />
                        </div>
                      ) : (
                        ele?.placeholder
                      )}
                    </button>
                  );
                })}
            </div>

            <div>
              {formQuery.fieldData
                ?.filter(
                  (item: any) =>
                    item.indexOrder === 3 && item?.type === "divider"
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div
                      key={index}
                      className="mt-7 mb-5 tracking-wide h-[1px] w-full bg-shadow bg-slate-300"
                    />
                  );
                })}
            </div>
            <div>
              {formQuery.fieldData
                ?.filter(
                  (item: any) =>
                    item.indexOrder === 4 && item?.type === "underline"
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div key={index} className="tracking-wide">
                      <p className="text-text-primary mb-1.5 text-sm text-center">
                        <button
                          onClick={() => router.push(`${item?.link}`)}
                          className="underline cursor-pointer"
                        >
                          {item?.text || item?.label}
                        </button>
                      </p>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      </div>
    </FormLayout>
  );
}
